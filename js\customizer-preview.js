/**
 * Customizer Live Preview for PNG Emblem
 *
 * @package Dakoii_Provincial_Government_Theme
 */

(function($) {
    'use strict';

    // PNG Emblem Image
    wp.customize('png_emblem_image', function(value) {
        value.bind(function(newval) {
            var $emblem = $('.png-emblem');
            var $emblemImage = $('.png-emblem-image');
            
            if (newval) {
                // Get the image URL from the attachment ID
                wp.media.attachment(newval).fetch().then(function() {
                    var imageUrl = wp.media.attachment(newval).get('url');
                    
                    if ($emblemImage.length) {
                        $emblemImage.attr('src', imageUrl);
                    } else {
                        $emblem.append('<img src="' + imageUrl + '" alt="PNG Emblem" class="png-emblem-image">');
                    }
                    
                    $emblem.addClass('has-custom-image');
                });
            } else {
                $emblemImage.remove();
                $emblem.removeClass('has-custom-image');
            }
        });
    });

    // PNG Emblem Text
    wp.customize('png_emblem_text', function(value) {
        value.bind(function(newval) {
            $('.emblem-text').text(newval);
        });
    });

    // Show/Hide PNG Emblem
    wp.customize('show_png_emblem', function(value) {
        value.bind(function(newval) {
            if (newval) {
                $('.national-emblem').show();
            } else {
                $('.national-emblem').hide();
            }
        });
    });

    // Color combinations data
    var colorCombinations = {
        'traditional': {
            'red': '#CE1126',
            'green': '#006A4E',
            'yellow': '#FFD700'
        },
        'sepik_heritage': {
            'red': '#B8860B',
            'green': '#228B22',
            'yellow': '#DAA520'
        },
        'cultural_celebration': {
            'red': '#DC143C',
            'green': '#008B8B',
            'yellow': '#FF8C00'
        }
    };

    // Helper function to calculate derived colors
    function hexToRgb(hex) {
        var result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
        return result ? {
            r: parseInt(result[1], 16),
            g: parseInt(result[2], 16),
            b: parseInt(result[3], 16)
        } : null;
    }

    function rgbToHex(r, g, b) {
        return "#" + ((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1);
    }

    function darkenColor(hex, percent) {
        var rgb = hexToRgb(hex);
        var factor = (100 - percent) / 100;

        var r = Math.max(0, Math.min(255, Math.round(rgb.r * factor)));
        var g = Math.max(0, Math.min(255, Math.round(rgb.g * factor)));
        var b = Math.max(0, Math.min(255, Math.round(rgb.b * factor)));

        return rgbToHex(r, g, b);
    }

    function lightenColor(hex, percent) {
        var rgb = hexToRgb(hex);
        var factor = percent / 100;

        var r = Math.max(0, Math.min(255, Math.round(rgb.r + (255 - rgb.r) * factor)));
        var g = Math.max(0, Math.min(255, Math.round(rgb.g + (255 - rgb.g) * factor)));
        var b = Math.max(0, Math.min(255, Math.round(rgb.b + (255 - rgb.b) * factor)));

        return rgbToHex(r, g, b);
    }

    function updateAllColors(red, green, yellow) {
        var colors = {
            '--png-red': red,
            '--png-green': green,
            '--png-yellow': yellow,
            '--dark-green': darkenColor(green, 25),
            '--light-green': lightenColor(green, 30),
            '--cream': '#FFF8DC',
            '--dark-brown': '#8B4513',
            '--official-blue': '#1e3a8a',
            '--light-gray': '#f8fafc',
            '--medium-gray': '#64748b'
        };

        var cssText = ':root { ';
        for (var property in colors) {
            cssText += property + ': ' + colors[property] + '; ';
        }
        cssText += '}';

        $('head').find('#nols-espa-dynamic-colors').remove();
        $('head').append('<style id="nols-espa-dynamic-colors">' + cssText + '</style>');
    }

    // Flag to prevent infinite loops
    var updatingFromCombination = false;

    // Color combination selector
    wp.customize('color_combination', function(value) {
        value.bind(function(newval) {
            if (newval !== 'custom' && colorCombinations[newval]) {
                updatingFromCombination = true;
                var colors = colorCombinations[newval];

                // Update individual color controls
                wp.customize('png_red_color').set(colors.red);
                wp.customize('png_green_color').set(colors.green);
                wp.customize('png_yellow_color').set(colors.yellow);

                // Update the CSS
                updateAllColors(colors.red, colors.green, colors.yellow);
                updatingFromCombination = false;
            }
        });
    });

    // Individual color controls
    wp.customize('png_red_color', function(value) {
        value.bind(function(newval) {
            if (!updatingFromCombination) {
                var green = wp.customize('png_green_color').get();
                var yellow = wp.customize('png_yellow_color').get();

                // Set combination to custom when individual colors are changed
                wp.customize('color_combination').set('custom');

                updateAllColors(newval, green, yellow);
            }
        });
    });

    wp.customize('png_green_color', function(value) {
        value.bind(function(newval) {
            if (!updatingFromCombination) {
                var red = wp.customize('png_red_color').get();
                var yellow = wp.customize('png_yellow_color').get();

                // Set combination to custom when individual colors are changed
                wp.customize('color_combination').set('custom');

                updateAllColors(red, newval, yellow);
            }
        });
    });

    wp.customize('png_yellow_color', function(value) {
        value.bind(function(newval) {
            if (!updatingFromCombination) {
                var red = wp.customize('png_red_color').get();
                var green = wp.customize('png_green_color').get();

                // Set combination to custom when individual colors are changed
                wp.customize('color_combination').set('custom');

                updateAllColors(red, green, newval);
            }
        });
    });

})(jQuery);
